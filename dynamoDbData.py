import boto3
import json
from botocore.exceptions import ClientError

REGION = "us-east-1"
JSON_FILE = "data.json"

dynamodb = boto3.resource("dynamodb", region_name=REGION)

# ---------- Tables Definition with GSIs ----------
TABLES = {
    "VendorScores": {
        "KeySchema": [{"AttributeName": "vendorCode", "KeyType": "HASH"}],
        "AttributeDefinitions": [{"AttributeName": "vendorCode", "AttributeType": "S"}],
        "GlobalSecondaryIndexes": []  # No GSI needed here
    },
    "SectionScores": {
        "KeySchema": [
            {"AttributeName": "vendorCode", "KeyType": "HASH"},
            {"AttributeName": "sectionKey", "KeyType": "RANGE"}
        ],
        "AttributeDefinitions": [
            {"AttributeName": "vendorCode", "AttributeType": "S"},
            {"AttributeName": "sectionKey", "AttributeType": "S"},
            {"AttributeName": "sectionTitle", "AttributeType": "S"}
        ],
        "GlobalSecondaryIndexes": [
            {
                "IndexName": "GSI2_sectionTitle",
                "KeySchema": [
                    {"AttributeName": "sectionTitle", "KeyType": "HASH"},
                    {"AttributeName": "vendorCode", "KeyType": "RANGE"}
                ],
                "Projection": {"ProjectionType": "ALL"},
            }
        ]
    },
    "QuestionResponses": {
        "KeySchema": [
            {"AttributeName": "vendorCode", "KeyType": "HASH"},
            {"AttributeName": "questionNumber", "KeyType": "RANGE"}
        ],
        "AttributeDefinitions": [
            {"AttributeName": "vendorCode", "AttributeType": "S"},
            {"AttributeName": "questionNumber", "AttributeType": "S"}
        ],
        "GlobalSecondaryIndexes": [
            {
                "IndexName": "GSI1_questionNumber",
                "KeySchema": [
                    {"AttributeName": "questionNumber", "KeyType": "HASH"},
                    {"AttributeName": "vendorCode", "KeyType": "RANGE"}
                ],
                "Projection": {"ProjectionType": "ALL"},
            }
        ]
    },
    "VendorScoresHistory": {
        "KeySchema": [
            {"AttributeName": "vendorCode", "KeyType": "HASH"},
            {"AttributeName": "modified_on", "KeyType": "RANGE"}
        ],
        "AttributeDefinitions": [
            {"AttributeName": "vendorCode", "AttributeType": "S"},
            {"AttributeName": "modified_on", "AttributeType": "S"}
        ],
        "GlobalSecondaryIndexes": []
    }
}

def create_tables():
    for name, schema in TABLES.items():
        try:
            table = dynamodb.Table(name)
            table.load()
            print(f"✅ Table {name} already exists")
        except ClientError:
            print(f"⚡ Creating table {name}")
            table = dynamodb.create_table(
                TableName=name,
                KeySchema=schema["KeySchema"],
                AttributeDefinitions=schema["AttributeDefinitions"],
                GlobalSecondaryIndexes=[
                    {
                        **gsi,
                        "ProvisionedThroughput": {"ReadCapacityUnits": 5, "WriteCapacityUnits": 5}
                    }
                    for gsi in schema.get("GlobalSecondaryIndexes", [])
                ],
                BillingMode="PAY_PER_REQUEST"
            )
            table.wait_until_exists()
            print(f"✅ Table {name} created")

def load_data():
    vendor_scores_table = dynamodb.Table("VendorScores")
    section_scores_table = dynamodb.Table("SectionScores")
    question_responses_table = dynamodb.Table("QuestionResponses")
    history_table = dynamodb.Table("VendorScoresHistory")

    with open(JSON_FILE, "r", encoding="utf-8") as f:
        data = json.load(f)

    for vendor in data:
        vendor_code = str(vendor["vendorCode"])
        modified_on = vendor["modified_on"]

        # VendorScores
        section_scores = {
            section["title"]: section["sectionTotalScore"]
            for section in vendor["response"]
        }
        vendor_scores_table.put_item(
            Item={
                "vendorCode": vendor_code,
                "vendorName": vendor["vendor"]["supplierName"],
                "modified_on": modified_on,
                "totalScore": vendor.get("supplierMSIScore", 0),
                "sectionScores": section_scores,
                "latestAssessmentId": vendor["supplierAssessmentAssignmentId"],
            }
        )

        # VendorScoresHistory
        history_table.put_item(
            Item={
                "vendorCode": vendor_code,
                "modified_on": modified_on,
                "totalScore": vendor.get("supplierMSIScore", 0),
                "sectionScores": section_scores,
            }
        )

        # SectionScores and QuestionResponses
        for section in vendor["response"]:
            for sub_section1 in section["assessmentSubSection1s"]:
                section_key = f"{section['id']}#{sub_section1['id']}"
                section_scores_table.put_item(
                    Item={
                        "vendorCode": vendor_code,
                        "sectionKey": section_key,
                        "sectionTitle": sub_section1["title"],
                        "parentSection": section["title"],
                        "totalScore": sub_section1["totalScore"],
                        "completed": sub_section1["totalCompleted"],
                        "modified_on": modified_on,
                    }
                )

                for sub_section2 in sub_section1["assessmentSubSection2s"]:
                    for question in sub_section2["form"]["data1"]:
                        selected_value = None
                        if question.get("values"):
                            for opt in question["values"]:
                                if opt.get("selected", False):
                                    selected_value = opt["value"]
                                    break
                        else:
                            selected_value = question.get("value")

                        question_responses_table.put_item(
                            Item={
                                "vendorCode": vendor_code,
                                "questionNumber": question["questionNumber"],
                                "label": question["label"],
                                "sectionTitle": section["title"],
                                "subSection1Title": sub_section1["title"],
                                "subSection2Title": sub_section2["title"],
                                "selectedValue": selected_value,
                                "ncRaised": question["nc"]["raised"],
                                "ofiRaised": question["ofi"]["raised"],
                                "modified_on": modified_on,
                            }
                        )

    print("🎯 Data successfully loaded into all tables")


if __name__ == "__main__":
    create_tables()
    load_data()
