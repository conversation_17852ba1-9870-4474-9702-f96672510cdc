import boto3
import logging
import uuid
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from nltk.tokenize import sent_tokenize
# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def insert_inline_citations(response_text, citations):
    """
    Insert inline citations into the response text based on similarity with citation text,
    without using NLTK.

    :param response_text: The generated response text from the model.
    :param citations: List of citation dictionaries.
    :return: Response text with inline citations.
    """
    if not citations:
        return response_text  # No citations to insert

    # Simple sentence tokenizer using regular expressions
    sentence_endings = re.compile(r'(?<=[.!?]) +')
    sentences = sentence_endings.split(response_text)

    # Prepare citation texts
    citation_texts = [cite['text'] for cite in citations]

    # Combine sentences and citation texts for vectorization
    texts = sentences + citation_texts

    # Compute TF-IDF vectors
    vectorizer = TfidfVectorizer().fit(texts)
    sentence_vectors = vectorizer.transform(sentences)
    citation_vectors = vectorizer.transform(citation_texts)

    # Initialize list to keep track of which sentences have which citations
    sentence_citations = [[] for _ in sentences]

    # Compute similarity between each sentence and each citation
    similarity_matrix = cosine_similarity(sentence_vectors, citation_vectors)

    # Threshold for similarity (adjust as needed)
    threshold = 0.2

    # For each sentence, find the citations with similarity above threshold
    for i, similarities in enumerate(similarity_matrix):
        for j, sim_score in enumerate(similarities):
            if sim_score > threshold:
                sentence_citations[i].append((j, sim_score))

    # Now, for each sentence, if it has citations, insert the citation markers
    modified_sentences = []
    for i, sentence in enumerate(sentences):
        if sentence_citations[i]:
            # Sort citations by similarity score descending
            sorted_citations = sorted(sentence_citations[i], key=lambda x: x[1], reverse=True)
            # Get the citation indices (add 1 to make it 1-based index)
            citation_indices = [str(citation_idx + 1) for citation_idx, _ in sorted_citations]
            # Create the citation marker
            citation_marker = '[' + ','.join(citation_indices) + ']'
            # Append the citation marker to the sentence
            modified_sentence = sentence.strip() + ' ' + citation_marker
        else:
            modified_sentence = sentence.strip()
        modified_sentences.append(modified_sentence)

    # Reconstruct the response text
    modified_text = ' '.join(modified_sentences)

    return modified_text

def retrieve_and_generate_example(client, knowledge_base_id, model_arn, prompt, session_id=None):
    """
    Use the retrieve_and_generate API to interact with the knowledge base and generate responses with citations.
    
    :param client: A low-level client representing the Amazon Bedrock Agents Runtime.
    :param knowledge_base_id: The ID of the knowledge base to use for retrieval.
    :param model_arn: The ARN of the model to use for generation.
    :param prompt: The input text for the request.
    :param session_id: (Optional) The session ID for stateful interactions.
    :return: The generated response text, associated citations, and session ID.
    """
    try:
        # Prepare the request payload
        request_params = {
            'input': {'text': prompt},
            'retrieveAndGenerateConfiguration': {
                'type': 'KNOWLEDGE_BASE',
                'knowledgeBaseConfiguration': {
                    'knowledgeBaseId': knowledge_base_id,
                    'modelArn': model_arn
                }
            }
        }

        # Include session ID if provided (for continuity)
        if session_id:
            request_params['sessionId'] = session_id

        # Make the API call to retrieve and generate a response
        response = client.retrieve_and_generate(**request_params)

        # Extract the session ID from the response
        new_session_id = response.get('sessionId')

        # Extract the response text
        response_text = response.get('output', {}).get('text', '')

        # Extract citations from the response
        citations = []
        for citation in response.get('citations', []):
            retrieved_refs = citation.get('retrievedReferences', [])
            for ref in retrieved_refs:
                # Extract citation details
                citation_info = {
                    'text': ref['content'].get('text', ''),
                    's3_uri': ref['location']['s3Location'].get('uri', ''),
                    'page_number': ref['metadata'].get('x-amz-bedrock-kb-document-page-number', None)
                }
                citations.append(citation_info)

        # Insert inline citations into the response text
        modified_text = insert_inline_citations(response_text, citations)

    except Exception as e:
        logger.error(f"An error occurred: {e}")
        return '', [], session_id

    return modified_text, citations, new_session_id

def main():
    region_name = 'us-east-1'  # Example AWS region, replace as needed
    knowledge_base_id = 'KAHRHGKX1Z'  # Replace with your knowledge base ID
    model_arn = 'amazon.titan-text-premier-v1:0'  # Replace with your model ARN
    prompt = 'cannot connect to s3'  # Example prompt

    try:
        # Initialize the Boto3 Bedrock Agents Runtime client
        client = boto3.client('bedrock-agent-runtime', region_name=region_name)

        # Initial call without a session ID
        response_text, citations, session_id = retrieve_and_generate_example(client, knowledge_base_id, model_arn, prompt)

        # Print the modified response text with inline citations
        print("\nResponse Text with Inline Citations:\n", response_text)

        # Print the citations with page numbers
        if citations:
            print("\nCitations with Page Numbers:")
            for i, cite in enumerate(citations):
                print(f"[{i+1}] Text: {cite['text'][:100]}...")  # Show first 100 characters of citation
                print(f"S3 URI: {cite['s3_uri']}")
                print(f"Page Number: {cite['page_number'] if cite['page_number'] is not None else 'N/A'}")

    except Exception as e:
        logger.error(f"An error occurred: {e}")

if __name__ == '__main__':
    main()
