import re
import boto3
import logging
import uuid
from botocore.exceptions import ClientError
from botocore.eventstream import EventStream  # Import EventStream from botocore

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define the BedrockAgentRuntimeWrapper class
class BedrockAgentRuntimeWrapper:
    """Encapsulates Amazon Bedrock Agents Runtime actions."""

    def __init__(self, client):
        """
        :param client: A low-level client representing the Amazon Bedrock Agents Runtime.
        """
        self.client = client

    def invoke_agent(self, agent_id, agent_alias_id, prompt, session_id=None):
        """
        Sends a prompt for the agent to process and respond to.

        :param agent_id: The unique identifier of the agent to use.
        :param agent_alias_id: The alias of the agent to use.
        :param prompt: The prompt that you want the agent to process.
        :param session_id: (Optional) The unique identifier of the session.
        :return: Model output and citations with text, S3 URL, and page number.
        """
        try:
            # If session ID is not provided, generate a random UUID
            if not session_id:
                session_id = str(uuid.uuid4())

            # Prepare the request parameters
            request_params = {
                'agentId': agent_id,
                'agentAliasId': agent_alias_id,
                'sessionId': session_id,
                'inputText': prompt,
            }

            # Make the API call to invoke the agent
            response = self.client.invoke_agent(**request_params)

            completion = ""
            citations = []

            # Iterate over response chunks to build the complete response
            if isinstance(response.get("completion"), EventStream):
                for event in response["completion"]:
                    # Decode the chunk to build the completion output
                    if "chunk" in event:
                        chunk_bytes = event["chunk"].get("bytes", b"")
                        chunk_text = chunk_bytes.decode()
                        completion += chunk_text

                    # Check if the chunk contains attribution with citations
                    if "attribution" in event["chunk"]:
                        citations_data = event["chunk"]["attribution"].get("citations", [])
                        for citation in citations_data:
                            if "retrievedReferences" in citation:
                                for ref in citation["retrievedReferences"]:
                                    citation_text = ref["content"].get("text", "")
                                    s3_uri = ref["location"]["s3Location"].get("uri", "")
                                    page_number = ref["metadata"].get("x-amz-bedrock-kb-document-page-number")

                                    # Add citation info to the list
                                    citations.append({
                                        "text": citation_text,
                                        "s3_uri": s3_uri,
                                        "page_number": page_number
                                    })

            # Extract inline citation references from the output
            inline_refs = re.findall(r'\[(\d+)\]', completion)
            inline_refs = list(set(int(ref) for ref in inline_refs))  # Remove duplicates and convert to int

            # Filter citations to match the inline references
            # filtered_citations = [citations[i - 1] for i in inline_refs if 0 < i <= len(citations)]

        except ClientError as e:
            logger.error(f"Couldn't invoke agent. {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            raise

        return completion, citations

def main():
    region_name = 'us-east-1'  # Example AWS region, replace as needed
    agent_id = '31KGQLYPML'  # Replace with your specific agent ID
    agent_alias_id = '0HFTSL8CNH'
    prompt = 'cannot connect to s3?'  # Example prompt

    try:
        # Initialize the Boto3 Bedrock Agent Runtime client
        client = boto3.client('bedrock-agent-runtime', region_name=region_name)

        # Create an instance of BedrockAgentRuntimeWrapper
        agent_wrapper = BedrockAgentRuntimeWrapper(client)

        # Invoke the agent with a prompt
        completion, citations = agent_wrapper.invoke_agent(agent_id, agent_a lias_id, prompt)

        # Print the model's complete response
        if completion:
            print("\nModel Output:\n", completion)

        # Print filtered citation information
        if citations:
            print("\nReferenced Citations:")
            for cite in citations:
                print(f"- Text: {cite['text'][:100]}...")  # Print the first 100 characters
                print(f"  S3 URI: {cite['s3_uri']}")
                print(f"  Page: {cite['page_number']}")

    except Exception as e:
        logger.error(f"An error occurred: {e}")

if __name__ == '__main__':
    main()
