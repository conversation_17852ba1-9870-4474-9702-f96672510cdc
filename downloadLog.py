import os
import requests

# Base URL
base_url = "http://13.235.57.225/eisqr-api/logs/"

# Exact filenames to download
file_names = [
    "requests15.log",  "requests16.log",  "requests17.log",  "requests18.log",
    "requests19.log",  "requests20.log",  "requests21.log",  "requests22.log",
    "requests23.log",  "requests24.log"  
]

# Directory to save files
save_dir = "./logs/"
os.makedirs(save_dir, exist_ok=True)

# Download each file with streaming
for file_name in file_names:
    url = f"{base_url}{file_name}"
    save_path = os.path.join(save_dir, file_name)
    
    print(f"⬇️  Downloading {file_name} ...")

    try:
        with requests.get(url, stream=True) as response:
            response.raise_for_status()
            with open(save_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=1024 * 1024):  # 1 MB chunks
                    if chunk:
                        f.write(chunk)
        print(f"✅ Finished: {file_name}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Failed to download {file_name}: {e}")
