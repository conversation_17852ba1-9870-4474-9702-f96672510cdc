import json
import pandas as pd
from datetime import datetime, timezone, timedelta

def convert_to_ist(utc_str):
    """Convert UTC timestamp string to IST in human-readable format."""
    try:
        utc_time = datetime.fromisoformat(utc_str.replace("Z", "+00:00"))
        ist_time = utc_time.astimezone(timezone(timedelta(hours=5, minutes=30)))
        return ist_time.strftime("%Y-%m-%d %H:%M:%S")
    except Exception:
        return None

def extract_data(json_path, output_excel_path):
    # Load the JSON data
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Collect rows
    rows = []
    for entry in data:
        message = entry.get('message', {})
        body = message.get('body', {})
        headers = message.get('headers', {})

        form_id = body.get('formId')
        ip_address = headers.get('x-forwarded-for')
        method = message.get('method')
        url = message.get('url')
        timestamp = convert_to_ist(message.get('timestamp') or entry.get('timestamp'))

        # Parse the 'score' field which is a JSON string
        try:
            score_data = json.loads(body.get('score', '{}'))
        except json.JSONDecodeError:
            score_data = {}

        row = {
            'formId': form_id,
            'ip_address': ip_address,
            'method': method,
            'url': url,
            'timestamp_IST': timestamp,
            'overallScore': score_data.get('overallScore'),
            'salesScore': score_data.get('salesScore'),
            'serviceScore': score_data.get('serviceScore')
        }
        rows.append(row)

    # Create DataFrame and sort by timestamp
    df = pd.DataFrame(rows)
    df = df.sort_values(by='timestamp_IST')

    # Save to Excel
    df.to_excel(output_excel_path, index=False)
    print(f"Excel file saved to: {output_excel_path}")

if __name__ == "__main__":
    extract_data("data.json", "extracted_data.xlsx")
